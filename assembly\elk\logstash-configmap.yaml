apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
data:
  logstash.conf: |
    input {
      kafka {
        bootstrap_servers => "kafka:9092"
        topics => ["order_topic"]
        group_id => "logstash"
        codec => "json"
      }
      file {
        path => "/var/log/app/*.log"
        start_position => "beginning"
      }
      tcp {
        port => 5000
      }
    }
    filter {
      grok {
        match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:loglevel} %{GREEDYDATA:message}" }
      }
    }
    output {
      elasticsearch {
        hosts => ["http://elasticsearch:9200"]
        index => "orders-%{+YYYY.MM.dd}"
      }
      stdout { codec => rubydebug }
    }
